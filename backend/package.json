{"name": "project-addis-backend", "version": "1.0.0", "description": "Backend API for project-addis", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf dist", "lint": "tsc --noEmit"}, "keywords": ["express", "api", "songs", "crud"], "author": "yo<PERSON>an afewerk", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node": "^24.0.15", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}