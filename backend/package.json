{"name": "song-management-backend", "version": "1.0.0", "description": "Backend API for song management application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "api", "songs", "crud"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "express-slow-down": "^2.1.0", "helmet": "^7.1.0", "morgan": "^1.10.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}