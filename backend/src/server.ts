import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import rateLimit from "express-rate-limit";
import slowDown from "express-slow-down";
import songsRouter from "./routes/songs";

const app = express();
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet());

// CORS configuration
app.use(
    cors({
        origin:
            process.env.NODE_ENV === "production"
                ? ["https://your-frontend-domain.com"]
                : ["http://localhost:3000", "http://localhost:8080"],
        credentials: true,
    })
);

// Rate limiting for demo tier
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
        tier: "demo",
        message:
            "Demo tier rate limit exceeded. Please wait before making more requests.",
        retryAfter: "15 minutes",
        upgradeInfo: "Upgrade to premium for higher limits",
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        res.status(429).json({
            tier: "demo",
            message:
                "Demo tier rate limit exceeded. Please wait before making more requests.",
            retryAfter: "15 minutes",
            upgradeInfo: "Upgrade to premium for higher limits",
        });
    },
});

// Speed limiting
const speedLimiter = slowDown({
    windowMs: 15 * 60 * 1000, // 15 minutes
    delayAfter: 50, // allow 50 requests per 15 minutes, then...
    delayMs: () => 500, // begin adding 500ms of delay per request above 50
    validate: { delayMs: false },
});

app.use(limiter);
app.use(speedLimiter);

// Logging
app.use(morgan("combined"));

// Body parsing middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Add demo tier headers to all responses
app.use((req, res, next) => {
    res.setHeader("x-ratelimit-tier", "demo");
    res.setHeader("x-ratelimit-remaining", "100"); // This would be dynamic in a real app
    res.setHeader("x-demo-notice", "This is a demo tier with limited requests");
    next();
});

// Health check endpoint
app.get("/api/health", (req, res) => {
    res.json({
        status: "OK",
        timestamp: new Date().toISOString(),
        tier: "demo",
    });
});

// Routes
app.use("/api/songs", songsRouter);

// 404 handler
app.use("*", (req, res) => {
    res.status(404).json({
        success: false,
        message: "Route not found",
    });
});

// Error handler
app.use(
    (
        err: any,
        req: express.Request,
        res: express.Response,
        next: express.NextFunction
    ) => {
        console.error(err.stack);
        res.status(500).json({
            success: false,
            message: "Something went wrong!",
            error:
                process.env.NODE_ENV === "development"
                    ? err.message
                    : undefined,
        });
    }
);

app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || "development"}`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
});
