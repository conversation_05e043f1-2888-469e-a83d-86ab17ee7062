import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";

const app = express();
const PORT = process.env.PORT || 5000;

// Trust proxy for accurate IP addresses (important for deployment)
app.set("trust proxy", 1);

// Basic middleware
app.use(helmet());
app.use(cors());
app.use(morgan("combined"));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Import routes
import songsRoutes from "./routes/songs";

// Routes
app.use("/api/songs", songsRoutes);

// Health check endpoint
app.get("/api/health", (req, res) => {
    res.json({
        status: "OK",
        message: "Song Management API is running",
        timestamp: new Date().toISOString(),
        deployment: {
            platform: "Render",
            environment: process.env.NODE_ENV || "production",
            version: "1.0.0",
        },
    });
});

// 404 handler
app.use("*", (req, res) => {
    res.status(404).json({
        error: "Not Found",
        message: `Route ${req.originalUrl} not found`,
    });
});

// Error handling middleware
app.use(
    (
        err: any,
        req: express.Request,
        res: express.Response,
        next: express.NextFunction
    ) => {
        console.error(err.stack);
        res.status(500).json({
            error: "Internal Server Error",
            message: "Something went wrong!",
        });
    }
);

// Start server
app.listen(PORT, () => {
    console.log(`🎵 Song Management API server is running on port ${PORT}`);
    console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
    console.log(`🎶 Songs API: http://localhost:${PORT}/api/songs`);
});

export default app;
