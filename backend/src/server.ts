import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";

import {
    demoTierMiddleware,
    healthCheckLimiter,
    DEMO_TIER_CONFIG,
} from "./middleware/rateLimiting";

const app = express();
const PORT = process.env.PORT || 5000;

// Trust proxy for accurate IP addresses (important for rate limiting)
app.set("trust proxy", 1);

// Basic middleware
app.use(helmet());
app.use(cors());
app.use(morgan("combined"));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Import routes
import songsRoutes from "./routes/songs";

// Routes with rate limiting
// Apply demo tier rate limiting to all API routes
app.use("/api/songs", demoTierMiddleware, songsRoutes);

// Health check endpoint with lighter rate limiting
app.get("/api/health", healthCheckLimiter, (req, res) => {
    res.json({
        status: "OK",
        message: "Song Management API is running (Production Ready)",
        timestamp: new Date().toISOString(),
        rateLimits: {
            tier: "demo",
            hourlyLimit: DEMO_TIER_CONFIG.hourlyLimit,
            minuteLimit: DEMO_TIER_CONFIG.minuteLimit,
            notice: "Production-ready demo with generous rate limits for testing.",
        },
        deployment: {
            platform: "Render",
            environment: process.env.NODE_ENV || "production",
            version: "1.0.0",
        },
    });
});

// Development-only endpoint to check rate limit status
if (process.env.NODE_ENV === 'development') {
    app.get("/api/rate-limit-status", (req, res) => {
        res.json({
            message: "Rate limit status endpoint (Development only)",
            config: DEMO_TIER_CONFIG,
            headers: {
                'X-RateLimit-Limit': req.get('X-RateLimit-Limit'),
                'X-RateLimit-Remaining': req.get('X-RateLimit-Remaining'),
                'X-RateLimit-Reset': req.get('X-RateLimit-Reset'),
            },
            timestamp: new Date().toISOString(),
        });
    });
}

// 404 handler
app.use("*", (req, res) => {
    res.status(404).json({
        error: "Not Found",
        message: `Route ${req.originalUrl} not found`,
    });
});

// Error handling middleware
app.use(
    (
        err: any,
        req: express.Request,
        res: express.Response,
        next: express.NextFunction
    ) => {
        console.error(err.stack);
        res.status(500).json({
            error: "Internal Server Error",
            message: "Something went wrong!",
        });
    }
);

// Start server
app.listen(PORT, () => {
    console.log(`🎵 Song Management API server is running on port ${PORT}`);
    console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
    console.log(`🎶 Songs API: http://localhost:${PORT}/api/songs`);
});

export default app;
