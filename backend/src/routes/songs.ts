import express from "express";
import { songs, Song } from "../data/songs";
import {
    ISong,
    CreateSongRequest,
    UpdateSongRequest,
    PaginationParams,
} from "../types/types";

const router = express.Router();

// In-memory storage (in a real app, this would be a database)
let songsDatabase: ISong[] = [...songs];

// Helper function to filter and paginate songs
const filterAndPaginateSongs = (
    allSongs: ISong[],
    params: PaginationParams
) => {
    const {
        page = 1,
        limit = 9,
        search = "",
        sortBy = "title",
        sortOrder = "asc",
    } = params;

    // Filter songs based on search query
    let filteredSongs = allSongs;
    if (search && search.trim()) {
        const searchLower = search.toLowerCase().trim();
        filteredSongs = allSongs.filter(
            (song) =>
                song.title.toLowerCase().includes(searchLower) ||
                song.artist.toLowerCase().includes(searchLower) ||
                song.album.toLowerCase().includes(searchLower) ||
                song.genre.toLowerCase().includes(searchLower) ||
                song.year.toString().includes(searchLower)
        );
    }

    // Sort songs
    filteredSongs.sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        // Handle different data types
        if (typeof aValue === "string") {
            aValue = aValue.toLowerCase();
            bValue = typeof bValue === "string" ? bValue.toLowerCase() : bValue;
        }

        if (sortOrder === "asc") {
            return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
            return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
    });

    // Calculate pagination
    const totalItems = filteredSongs.length;
    const totalPages = Math.ceil(totalItems / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedSongs = filteredSongs.slice(startIndex, endIndex);

    return {
        data: paginatedSongs,
        pagination: {
            currentPage: page,
            totalPages,
            totalItems,
            itemsPerPage: limit,
        },
    };
};

// GET /api/songs - Get all songs with optional filtering and pagination
router.get("/", (req, res) => {
    try {
        const params: PaginationParams = {
            page: parseInt(req.query.page as string) || 1,
            limit: parseInt(req.query.limit as string) || 9,
            search: (req.query.search as string) || "",
            sortBy: (req.query.sortBy as keyof ISong) || "title",
            sortOrder: (req.query.sortOrder as "asc" | "desc") || "asc",
        };

        const result = filterAndPaginateSongs(songsDatabase, params);

        res.json(result);
    } catch (error) {
        console.error("Error fetching songs:", error);
        res.status(500).json({
            success: false,
            message: "Failed to fetch songs",
            error:
                process.env.NODE_ENV === "development"
                    ? (error as Error).message
                    : undefined,
        });
    }
});

// GET /api/songs/search - Search songs (alternative endpoint)
router.get("/search", (req, res) => {
    try {
        const searchQuery = (req.query.q as string) || "";
        const params: PaginationParams = {
            page: parseInt(req.query.page as string) || 1,
            limit: parseInt(req.query.limit as string) || 9,
            search: searchQuery,
            sortBy: (req.query.sortBy as keyof ISong) || "title",
            sortOrder: (req.query.sortOrder as "asc" | "desc") || "asc",
        };

        const result = filterAndPaginateSongs(songsDatabase, params);

        res.json(result);
    } catch (error) {
        console.error("Error searching songs:", error);
        res.status(500).json({
            success: false,
            message: "Failed to search songs",
            error:
                process.env.NODE_ENV === "development"
                    ? (error as Error).message
                    : undefined,
        });
    }
});

// GET /api/songs/:id - Get a specific song by ID
router.get("/:id", (req, res) => {
    try {
        const { id } = req.params;
        const song = songsDatabase.find((s) => s.id === id);

        if (!song) {
            return res.status(404).json({
                success: false,
                message: "Song not found",
            });
        }

        res.json(song);
    } catch (error) {
        console.error("Error fetching song:", error);
        res.status(500).json({
            success: false,
            message: "Failed to fetch song",
            error:
                process.env.NODE_ENV === "development"
                    ? (error as Error).message
                    : undefined,
        });
    }
});

// POST /api/songs - Create a new song
router.post("/", (req, res) => {
    try {
        const songData: CreateSongRequest = req.body;

        // Validate required fields
        const requiredFields = [
            "title",
            "artist",
            "album",
            "year",
            "genre",
            "duration",
        ];
        const missingFields = requiredFields.filter(
            (field) => !songData[field as keyof CreateSongRequest]
        );

        if (missingFields.length > 0) {
            return res.status(400).json({
                success: false,
                message: `Missing required fields: ${missingFields.join(", ")}`,
            });
        }

        // Create new song
        const newSong = new Song(songData);
        songsDatabase.unshift(newSong);

        res.status(201).json(newSong);
    } catch (error) {
        console.error("Error creating song:", error);
        res.status(500).json({
            success: false,
            message: "Failed to create song",
            error:
                process.env.NODE_ENV === "development"
                    ? (error as Error).message
                    : undefined,
        });
    }
});

// PUT /api/songs/:id - Update a song
router.put("/:id", (req, res) => {
    try {
        const { id } = req.params;
        const updateData: UpdateSongRequest = req.body;

        const songIndex = songsDatabase.findIndex((s) => s.id === id);

        if (songIndex === -1) {
            return res.status(404).json({
                success: false,
                message: "Song not found",
            });
        }

        // Update song
        const updatedSong = {
            ...songsDatabase[songIndex],
            ...updateData,
            updatedAt: new Date().toISOString(),
        };

        songsDatabase[songIndex] = updatedSong;

        res.json(updatedSong);
    } catch (error) {
        console.error("Error updating song:", error);
        res.status(500).json({
            success: false,
            message: "Failed to update song",
            error:
                process.env.NODE_ENV === "development"
                    ? (error as Error).message
                    : undefined,
        });
    }
});

// DELETE /api/songs/:id - Delete a song
router.delete("/:id", (req, res) => {
    try {
        const { id } = req.params;
        const songIndex = songsDatabase.findIndex((s) => s.id === id);

        if (songIndex === -1) {
            return res.status(404).json({
                success: false,
                message: "Song not found",
            });
        }

        const deletedSong = songsDatabase[songIndex];
        songsDatabase.splice(songIndex, 1);

        res.json({
            success: true,
            message: "Song deleted successfully",
            data: deletedSong,
        });
    } catch (error) {
        console.error("Error deleting song:", error);
        res.status(500).json({
            success: false,
            message: "Failed to delete song",
            error:
                process.env.NODE_ENV === "development"
                    ? (error as Error).message
                    : undefined,
        });
    }
});

export default router;
