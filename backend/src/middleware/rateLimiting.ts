import rateLimit from "express-rate-limit";
import slowDown from "express-slow-down";
import { Request, Response, NextFunction } from "express";

// Environment-based configuration for flexible deployment
const isDevelopment = process.env.NODE_ENV === "development";
const isProduction = process.env.NODE_ENV === "production";

const DEMO_TIER_CONFIG = {
    // Hourly limits - more generous for production demo
    hourlyLimit: isDevelopment ? 10000 : 2000, // Dev: 10k/hour, Prod: 2k/hour
    // Per-minute limits for burst protection
    minuteLimit: isDevelopment ? 200 : 100, // Dev: 200/min, Prod: 100/min
    // Slow down configuration
    slowDownDelay: isDevelopment ? 100 : 50, // Start delay after more requests in dev
    slowDownDelayMs: isDevelopment ? 100 : 200, // Shorter delay in dev
    slowDownMaxDelayMs: isDevelopment ? 500 : 1000, // Max delay
};

// Demo tier hourly rate limiter
const demoHourlyLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: DEMO_TIER_CONFIG.hourlyLimit,
    message: {
        error: "Demo Tier Rate Limit Exceeded",
        message: `Demo tier allows ${DEMO_TIER_CONFIG.hourlyLimit} requests per hour. Please upgrade for higher limits.`,
        retryAfter: "1 hour",
        tier: "demo",
        upgradeInfo:
            "Contact support to upgrade your account for higher rate limits.",
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
        console.warn(
            `Rate limit exceeded for IP: ${req.ip} - Hourly limit reached`
        );
        res.status(429).json({
            error: "Demo Tier Rate Limit Exceeded",
            message: `Demo tier allows ${DEMO_TIER_CONFIG.hourlyLimit} requests per hour. Please upgrade for higher limits.`,
            retryAfter: "1 hour",
            tier: "demo",
            upgradeInfo:
                "Contact support to upgrade your account for higher rate limits.",
            timestamp: new Date().toISOString(),
            limits: {
                hourly: DEMO_TIER_CONFIG.hourlyLimit,
                perMinute: DEMO_TIER_CONFIG.minuteLimit,
            },
        });
    },
});

// Demo tier per-minute rate limiter for burst protection
const demoMinuteLimiter = rateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: DEMO_TIER_CONFIG.minuteLimit,
    message: {
        error: "Demo Tier Burst Limit Exceeded",
        message: `Demo tier allows ${DEMO_TIER_CONFIG.minuteLimit} requests per minute. Please slow down.`,
        retryAfter: "1 minute",
        tier: "demo",
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
        console.warn(
            `Rate limit exceeded for IP: ${req.ip} - Per-minute limit reached`
        );
        res.status(429).json({
            error: "Demo Tier Burst Limit Exceeded",
            message: `Demo tier allows ${DEMO_TIER_CONFIG.minuteLimit} requests per minute. Please slow down.`,
            retryAfter: "1 minute",
            tier: "demo",
            timestamp: new Date().toISOString(),
            limits: {
                hourly: DEMO_TIER_CONFIG.hourlyLimit,
                perMinute: DEMO_TIER_CONFIG.minuteLimit,
            },
        });
    },
});

// Slow down middleware for demo tier
const demoSlowDown = slowDown({
    windowMs: 60 * 1000, // 1 minute
    delayAfter: DEMO_TIER_CONFIG.slowDownDelay, // Allow 5 requests per minute at full speed
    delayMs: () => DEMO_TIER_CONFIG.slowDownDelayMs, // Fixed function format for v2
    maxDelayMs: DEMO_TIER_CONFIG.slowDownMaxDelayMs, // Maximum delay of 2 seconds
    validate: {
        delayMs: false, // Disable delayMs validation warning
    },
});

// Middleware to add demo tier headers
const addDemoTierHeaders = (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    // Add custom headers to indicate this is a demo tier
    res.set({
        "X-RateLimit-Tier": "demo",
        "X-RateLimit-Hourly-Limit": DEMO_TIER_CONFIG.hourlyLimit.toString(),
        "X-RateLimit-Minute-Limit": DEMO_TIER_CONFIG.minuteLimit.toString(),
        "X-Demo-Notice":
            "This is a demo tier with limited requests. Upgrade for higher limits.",
    });
    next();
};

// Combined demo tier middleware
const demoTierMiddleware = [
    addDemoTierHeaders,
    demoHourlyLimiter,
    demoMinuteLimiter,
    demoSlowDown,
];

// Health check rate limiter (very lenient for monitoring)
const healthCheckLimiter = rateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: 120, // Allow 120 health checks per minute (2 per second)
    message: {
        error: "Health Check Rate Limit Exceeded",
        message: "Too many health check requests. Please slow down.",
        retryAfter: "1 minute",
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
        // Skip rate limiting for certain monitoring services
        const userAgent = req.get("User-Agent") || "";
        return (
            userAgent.includes("Render") ||
            userAgent.includes("UptimeRobot") ||
            userAgent.includes("StatusCake") ||
            userAgent.includes("Pingdom")
        );
    },
});

// API documentation rate limiter
const docsLimiter = rateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: 20, // Allow 20 requests per minute for docs
    message: {
        error: "Documentation Rate Limit Exceeded",
        message: "Too many documentation requests. Please slow down.",
        retryAfter: "1 minute",
    },
    standardHeaders: true,
    legacyHeaders: false,
});

export {
    demoTierMiddleware,
    demoHourlyLimiter,
    demoMinuteLimiter,
    demoSlowDown,
    addDemoTierHeaders,
    healthCheckLimiter,
    docsLimiter,
    DEMO_TIER_CONFIG,
};
