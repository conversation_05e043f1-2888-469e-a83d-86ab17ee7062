const { v4: uuidv4 } = require("uuid");

// Song data structure
class Song {
    constructor({ title, artist, album, year, genre, duration }) {
        this.id = uuidv4();
        this.title = title;
        this.artist = artist;
        this.album = album;
        this.year = year;
        this.genre = genre;
        this.duration = duration;
        this.createdAt = new Date().toISOString();
        this.updatedAt = new Date().toISOString();
    }
}

// Dummy song data
const songsData = [
    {
        title: "Bohemian Rhapsody",
        artist: "Queen",
        album: "A Night at the Opera",
        year: 1975,
        genre: "Rock",
        duration: "5:55",
    },
    {
        title: "Hotel California",
        artist: "Eagles",
        album: "Hotel California",
        year: 1976,
        genre: "Rock",
        duration: "6:30",
    },
    {
        title: "Stairway to Heaven",
        artist: "Led Zeppelin",
        album: "Led Zeppelin IV",
        year: 1971,
        genre: "Rock",
        duration: "8:02",
    },
    {
        title: "Imagine",
        artist: "<PERSON>",
        album: "Imagine",
        year: 1971,
        genre: "Pop",
        duration: "3:07",
    },
    {
        title: "<PERSON> Jean",
        artist: "<PERSON>",
        album: "Thriller",
        year: 1982,
        genre: "Pop",
        duration: "4:54",
    },
    {
        title: "Like a Rolling Stone",
        artist: "Bob Dylan",
        album: "Highway 61 Revisited",
        year: 1965,
        genre: "Folk Rock",
        duration: "6:13",
    },
    {
        title: "Smells Like Teen Spirit",
        artist: "Nirvana",
        album: "Nevermind",
        year: 1991,
        genre: "Grunge",
        duration: "5:01",
    },
    {
        title: "What's Going On",
        artist: "Marvin Gaye",
        album: "What's Going On",
        year: 1971,
        genre: "Soul",
        duration: "3:53",
    },
    {
        title: "Purple Haze",
        artist: "Jimi Hendrix",
        album: "Are You Experienced",
        year: 1967,
        genre: "Rock",
        duration: "2:50",
    },
    {
        title: "Good Vibrations",
        artist: "The Beach Boys",
        album: "Smiley Smile",
        year: 1966,
        genre: "Pop",
        duration: "3:39",
    },
    {
        title: "Respect",
        artist: "Aretha Franklin",
        album: "I Never Loved a Man",
        year: 1967,
        genre: "Soul",
        duration: "2:28",
    },
    {
        title: "Hey Jude",
        artist: "The Beatles",
        album: "Hey Jude",
        year: 1968,
        genre: "Pop",
        duration: "7:11",
    },
    {
        title: "Johnny B. Goode",
        artist: "Chuck Berry",
        album: "Chuck Berry Is on Top",
        year: 1958,
        genre: "Rock and Roll",
        duration: "2:38",
    },
    {
        title: "I Can't Get No Satisfaction",
        artist: "The Rolling Stones",
        album: "Out of Our Heads",
        year: 1965,
        genre: "Rock",
        duration: "3:45",
    },
    {
        title: "My Generation",
        artist: "The Who",
        album: "The Who Sings My Generation",
        year: 1965,
        genre: "Rock",
        duration: "3:18",
    },
    {
        title: "Born to Run",
        artist: "Bruce Springsteen",
        album: "Born to Run",
        year: 1975,
        genre: "Rock",
        duration: "4:31",
    },
    {
        title: "Bridge Over Troubled Water",
        artist: "Simon and Garfunkel",
        album: "Bridge Over Troubled Water",
        year: 1970,
        genre: "Folk",
        duration: "4:55",
    },
    {
        title: "The Sound of Silence",
        artist: "Simon and Garfunkel",
        album: "Sounds of Silence",
        year: 1965,
        genre: "Folk",
        duration: "3:05",
    },
    {
        title: "Waterloo Sunset",
        artist: "The Kinks",
        album: "Something Else",
        year: 1967,
        genre: "Rock",
        duration: "3:12",
    },
    {
        title: "A Day in the Life",
        artist: "The Beatles",
        album: "Sgt. Pepper's Lonely Hearts Club Band",
        year: 1967,
        genre: "Psychedelic Rock",
        duration: "5:39",
    },
    {
        title: "Superstition",
        artist: "Stevie Wonder",
        album: "Talking Book",
        year: 1972,
        genre: "Funk",
        duration: "4:26",
    },
    {
        title: "Dancing Queen",
        artist: "ABBA",
        album: "Arrival",
        year: 1976,
        genre: "Pop",
        duration: "3:51",
    },
    {
        title: "Sweet Child O' Mine",
        artist: "Guns N' Roses",
        album: "Appetite for Destruction",
        year: 1987,
        genre: "Hard Rock",
        duration: "5:03",
    },
    {
        title: "Thunderstruck",
        artist: "AC/DC",
        album: "The Razors Edge",
        year: 1990,
        genre: "Hard Rock",
        duration: "4:52",
    },
    {
        title: "Lose Yourself",
        artist: "Eminem",
        album: "8 Mile Soundtrack",
        year: 2002,
        genre: "Hip Hop",
        duration: "5:26",
    },
    {
        title: "Crazy",
        artist: "Gnarls Barkley",
        album: "St. Elsewhere",
        year: 2006,
        genre: "Soul",
        duration: "2:59",
    },
    {
        title: "Rehab",
        artist: "Amy Winehouse",
        album: "Back to Black",
        year: 2006,
        genre: "Soul",
        duration: "3:35",
    },
    {
        title: "Somebody That I Used to Know",
        artist: "Gotye",
        album: "Making Mirrors",
        year: 2011,
        genre: "Indie Pop",
        duration: "4:04",
    },
    {
        title: "Rolling in the Deep",
        artist: "Adele",
        album: "21",
        year: 2010,
        genre: "Soul",
        duration: "3:48",
    },
    {
        title: "Shape of You",
        artist: "Ed Sheeran",
        album: "÷",
        year: 2017,
        genre: "Pop",
        duration: "3:53",
    },
    {
        title: "Blinding Lights",
        artist: "The Weeknd",
        album: "After Hours",
        year: 2019,
        genre: "Synthpop",
        duration: "3:20",
    },
    {
        title: "Bad Guy",
        artist: "Billie Eilish",
        album: "When We All Fall Asleep",
        year: 2019,
        genre: "Electropop",
        duration: "3:14",
    },
    {
        title: "Watermelon Sugar",
        artist: "Harry Styles",
        album: "Fine Line",
        year: 2019,
        genre: "Pop Rock",
        duration: "2:54",
    },
    {
        title: "Levitating",
        artist: "Dua Lipa",
        album: "Future Nostalgia",
        year: 2020,
        genre: "Disco Pop",
        duration: "3:23",
    },
    {
        title: "Good 4 U",
        artist: "Olivia Rodrigo",
        album: "Sour",
        year: 2021,
        genre: "Pop Punk",
        duration: "2:58",
    },
    {
        title: "Stay",
        artist: "The Kid LAROI & Justin Bieber",
        album: "F*ck Love 3",
        year: 2021,
        genre: "Pop",
        duration: "2:21",
    },
    {
        title: "Heat Waves",
        artist: "Glass Animals",
        album: "Dreamland",
        year: 2020,
        genre: "Indie Pop",
        duration: "3:58",
    },
    {
        title: "As It Was",
        artist: "Harry Styles",
        album: "Harry's House",
        year: 2022,
        genre: "Pop",
        duration: "2:47",
    },
    {
        title: "Anti-Hero",
        artist: "Taylor Swift",
        album: "Midnights",
        year: 2022,
        genre: "Pop",
        duration: "3:20",
    },
    {
        title: "Flowers",
        artist: "Miley Cyrus",
        album: "Endless Summer Vacation",
        year: 2023,
        genre: "Pop",
        duration: "3:20",
    },
    {
        title: "Paint It Black",
        artist: "The Rolling Stones",
        album: "Aftermath",
        year: 1966,
        genre: "Rock",
        duration: "3:45",
    },
    {
        title: "Karma Police",
        artist: "Radiohead",
        album: "OK Computer",
        year: 1997,
        genre: "Alternative Rock",
        duration: "4:21",
    },
    {
        title: "No Woman, No Cry",
        artist: "Bob Marley & The Wailers",
        album: "Natty Dread",
        year: 1974,
        genre: "Reggae",
        duration: "4:06",
    },
    {
        title: "Boogie Wonderland",
        artist: "Earth, Wind & Fire",
        album: "I Am",
        year: 1979,
        genre: "Disco",
        duration: "4:48",
    },
    {
        title: "Come As You Are",
        artist: "Nirvana",
        album: "Nevermind",
        year: 1991,
        genre: "Grunge",
        duration: "3:39",
    },
    {
        title: "Dream On",
        artist: "Aerosmith",
        album: "Aerosmith",
        year: 1973,
        genre: "Hard Rock",
        duration: "4:26",
    },
    {
        title: "Enter Sandman",
        artist: "Metallica",
        album: "Metallica",
        year: 1991,
        genre: "Heavy Metal",
        duration: "5:32",
    },
    {
        title: "Hallelujah",
        artist: "Leonard Cohen",
        album: "Various Positions",
        year: 1984,
        genre: "Folk",
        duration: "4:36",
    },
    {
        title: "Wonderwall",
        artist: "Oasis",
        album: "(What's the Story) Morning Glory?",
        year: 1995,
        genre: "Britpop",
        duration: "4:18",
    },
    {
        title: "Creep",
        artist: "Radiohead",
        album: "Pablo Honey",
        year: 1993,
        genre: "Alternative Rock",
        duration: "3:56",
    },
    {
        title: "Take On Me",
        artist: "a-ha",
        album: "Hunting High and Low",
        year: 1985,
        genre: "Synthpop",
        duration: "3:46",
    },
    {
        title: "Just the Way You Are",
        artist: "Bruno Mars",
        album: "Doo-Wops & Hooligans",
        year: 2010,
        genre: "Pop",
        duration: "3:40",
    },
    {
        title: "Uptown Funk",
        artist: "Mark Ronson ft. Bruno Mars",
        album: "Uptown Special",
        year: 2014,
        genre: "Funk Pop",
        duration: "4:30",
    },
    {
        title: "Thinking Out Loud",
        artist: "Ed Sheeran",
        album: "x",
        year: 2014,
        genre: "Pop Soul",
        duration: "4:41",
    },
    {
        title: "Shallow",
        artist: "Lady Gaga & Bradley Cooper",
        album: "A Star Is Born",
        year: 2018,
        genre: "Pop Rock",
        duration: "3:37",
    },
    {
        title: "Can't Stop the Feeling!",
        artist: "Justin Timberlake",
        album: "Trolls: Original Motion Picture Soundtrack",
        year: 2016,
        genre: "Pop",
        duration: "3:56",
    },
    {
        title: "Sunflower",
        artist: "Post Malone & Swae Lee",
        album: "Spider-Man: Into the Spider-Verse",
        year: 2018,
        genre: "Hip Hop",
        duration: "2:38",
    },
    {
        title: "Rockstar",
        artist: "Post Malone ft. 21 Savage",
        album: "Beerbongs & Bentleys",
        year: 2017,
        genre: "Trap",
        duration: "3:38",
    },
    {
        title: "God's Plan",
        artist: "Drake",
        album: "Scorpion",
        year: 2018,
        genre: "Hip Hop",
        duration: "3:18",
    },
    {
        title: "Sicko Mode",
        artist: "Travis Scott",
        album: "Astroworld",
        year: 2018,
        genre: "Hip Hop",
        duration: "5:12",
    },
    {
        title: "Peaches",
        artist: "Justin Bieber ft. Daniel Caesar & Giveon",
        album: "Justice",
        year: 2021,
        genre: "R&B",
        duration: "3:18",
    },
    {
        title: "Old Town Road",
        artist: "Lil Nas X ft. Billy Ray Cyrus",
        album: "7",
        year: 2019,
        genre: "Country Rap",
        duration: "2:37",
    },
    {
        title: "MONTERO (Call Me By Your Name)",
        artist: "Lil Nas X",
        album: "MONTERO",
        year: 2021,
        genre: "Pop Rap",
        duration: "2:18",
    },
    {
        title: "Industry Baby",
        artist: "Lil Nas X & Jack Harlow",
        album: "MONTERO",
        year: 2021,
        genre: "Hip Hop",
        duration: "3:32",
    },
    {
        title: "Happier Than Ever",
        artist: "Billie Eilish",
        album: "Happier Than Ever",
        year: 2021,
        genre: "Alt Pop",
        duration: "4:58",
    },
    {
        title: "Positions",
        artist: "Ariana Grande",
        album: "Positions",
        year: 2020,
        genre: "R&B Pop",
        duration: "2:52",
    },
    {
        title: "Thank U, Next",
        artist: "Ariana Grande",
        album: "Thank U, Next",
        year: 2019,
        genre: "Pop",
        duration: "3:27",
    },
    {
        title: "Break My Soul",
        artist: "Beyoncé",
        album: "Renaissance",
        year: 2022,
        genre: "House",
        duration: "4:38",
    },
    {
        title: "Cuff It",
        artist: "Beyoncé",
        album: "Renaissance",
        year: 2022,
        genre: "Disco Funk",
        duration: "3:45",
    },
    {
        title: "Save Your Tears",
        artist: "The Weeknd",
        album: "After Hours",
        year: 2020,
        genre: "Synthpop",
        duration: "3:35",
    },
    {
        title: "Die for You",
        artist: "The Weeknd",
        album: "Starboy",
        year: 2016,
        genre: "Alt R&B",
        duration: "4:20",
    },
    {
        title: "Kill Bill",
        artist: "SZA",
        album: "SOS",
        year: 2022,
        genre: "R&B",
        duration: "2:33",
    },
    {
        title: "Ghost",
        artist: "Justin Bieber",
        album: "Justice",
        year: 2021,
        genre: "Pop",
        duration: "2:33",
    },
    {
        title: "Escapism.",
        artist: "RAYE ft. 070 Shake",
        album: "My 21st Century Blues",
        year: 2022,
        genre: "Alt Pop",
        duration: "4:32",
    },
    {
        title: "Die for My Bitch",
        artist: "Baby Keem",
        album: "Die for My Bitch",
        year: 2019,
        genre: "Hip Hop",
        duration: "3:03",
    },
    {
        title: "Bad Habit",
        artist: "Steve Lacy",
        album: "Gemini Rights",
        year: 2022,
        genre: "Alt R&B",
        duration: "3:52",
    },
    {
        title: "Get Lucky",
        artist: "Daft Punk ft. Pharrell Williams",
        album: "Random Access Memories",
        year: 2013,
        genre: "Disco Funk",
        duration: "6:09",
    },
    {
        title: "Royals",
        artist: "Lorde",
        album: "Pure Heroine",
        year: 2013,
        genre: "Electropop",
        duration: "3:10",
    },
    {
        title: "Team",
        artist: "Lorde",
        album: "Pure Heroine",
        year: 2013,
        genre: "Electropop",
        duration: "3:13",
    },
    {
        title: "Elastic Heart",
        artist: "Sia",
        album: "1000 Forms of Fear",
        year: 2014,
        genre: "Pop",
        duration: "4:17",
    },
];

// Create song instances
let songs = songsData.map((songData) => new Song(songData));

module.exports = {
    Song,
    songs,
};
