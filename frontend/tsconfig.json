{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "Node", "jsx": "react-jsx", "allowJs": true, "esModuleInterop": true, "resolveJsonModule": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@app-types/*": ["src/types/*"], "@pages/*": ["src/pages/*"], "@store/*": ["src/store/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@styles/*": ["src/styles/*"]}}, "include": ["webpack.config.ts", "src"], "exclude": ["node_modules", "dist"]}