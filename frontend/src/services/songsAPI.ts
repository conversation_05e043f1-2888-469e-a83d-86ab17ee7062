import { store } from "@/store/store";
import axios from "axios";
import { addNotification } from "@/store/slices/uiSlice";
import { ISong } from "@/types/song";

// Fetch songs API - Fetches all the songs
const api = axios.create({
    baseURL:
        process.env.NODE_ENV === "production"
            ? "/api"
            : "http://localhost:5000/api",
    timeout: 10000,
    headers: {
        "Content-Type": "application/json",
    },
});

// Response Interceptor
api.interceptors.response.use(
    response => {
        // Am gonna need this to make sure my render free tier doesn't run out
        const rateLimitTier = response.headers["x-ratelimit-tier"];
        const rateLimitRemaining = response.headers["x-ratelimit-remaining"];
        const demoNotice = response.headers["x-demo-notice"];

        if (rateLimitTier === "demo" && rateLimitRemaining) {
            console.warn(
                `Demo tier: ${rateLimitTier}, Remaining requests: ${rateLimitRemaining}. ${demoNotice}`
            );
            if (parseInt(rateLimitRemaining) < 10) {
                console.warn("You are running low on requests.");
                window.alert("You are running low on requests.");
            }
        }
        return response;
    },
    error => {
        if (error.response?.status === 429) {
            const errorData = error.response.data;
            console.warn("Rate limit exceeded:", errorData);

            if (errorData.tier === "demo") {
                const message =
                    errorData.message ||
                    "Demo tier rate limit exceeded. Please wait before making more requests.";
                console.error("Demo Rate Limit:", message);

                store.dispatch(
                    addNotification({
                        type: "error",
                        message: message,
                        duration: 5000, // Show for 5 seconds
                    })
                );

                // Add rate limit info to error for component handling
                error.rateLimitInfo = {
                    tier: errorData.tier,
                    retryAfter: errorData.retryAfter,
                    upgradeInfo: errorData.upgradeInfo,
                    message: errorData.message,
                };
            }
        } else if (error.response?.status === 500) {
            console.error("Server error:", error.response.data);
        }
        return Promise.reject(error);
    }
);

// Songs API functions
export const fetchSongs = async () => {
    const response = await api.get("/songs");
    return response;
};

export const createSong = async (songData: ISong) => {
    const response = await api.post("/songs", songData);
    return response;
};

export const updateSong = async (id: string, songData: ISong) => {
    const response = await api.put(`/songs/${id}`, songData);
    return response;
};
export const deleteSong = async (id: string) => {
    const response = await api.delete(`/songs/${id}`);
    return response;
};

export const searchSongs = async (query: string) => {
    const response = await api.get(`/songs/search`, {
        params: { q: query },
    });
    return response;
};

// Health Check
export const healthCheck = async () => {
    const response = await api.get("/health");
    return response;
};

export default api;
