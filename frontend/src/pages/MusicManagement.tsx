import { useEffect } from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>erLeft,
    HeaderRight,
    AddButton,
    ContentArea,
    StatsBar,
} from "./MusicManagement.style";
import SearchBar from "@components/SearchBar";
import SortControls from "@components/SortControls";
import ThemeSwitcher from "@components/ThemeSwitcher";
import LoadingSpinner from "@/components/LoadingSpinner";
import SongList from "@components/SongList";
import ErrorMessage from "@/components/ErrorMessage";
import Pagination from "@components/Pagination";
import SongForm from "@/components/SongForm";
import DeleteConfirmModal from "@/components/DeleteConfirmModal";
import { useDispatch, useSelector } from "react-redux";
import { openCreateModal } from "@/store/slices/uiSlice";
import { RootState } from "@store/store";
import { fetchSongsRequest, setCurrentPage } from "@/store/slices/songsSlice";
import AOS from "aos";

const MusicManagement = () => {
    const dispatch = useDispatch();
    const { songs, pagination, isLoading, error, searchQuery } = useSelector(
        (state: RootState) => state.songs
    );
    useEffect(() => {
        dispatch(fetchSongsRequest());
    }, [dispatch]);

    useEffect(() => {
        AOS.refresh();
    }, [songs]);

    const handlePageChange = (page: number) => {
        dispatch(setCurrentPage(page));
    };

    const handleAddSong = () => {
        dispatch(openCreateModal());
    };

    if (isLoading && !songs.length) {
        return (
            <Container>
                <LoadingSpinner />
            </Container>
        );
    }

    return (
        <Container>
            <Header data-aos="fade-down" data-aos-delay="100">
                <HeaderLeft>
                    <div data-aos="fade-right" data-aos-delay="300">
                        <SearchBar />
                    </div>
                    <div data-aos="fade-right" data-aos-delay="400">
                        <SortControls />
                    </div>
                </HeaderLeft>
                <HeaderRight>
                    <div data-aos="fade-left" data-aos-delay="300">
                        <ThemeSwitcher />
                    </div>
                    <div data-aos="fade-left" data-aos-delay="400">
                        <AddButton onClick={handleAddSong}>
                            Add New Song
                        </AddButton>
                    </div>
                </HeaderRight>
            </Header>
            {error && <ErrorMessage message={error} />}

            <ContentArea data-aos="fade-up" data-aos-delay="500">
                <StatsBar data-aos="fade-in" data-aos-delay="600">
                    <span>
                        {searchQuery ? (
                            <>
                                Showing {songs.length} of{" "}
                                {pagination.totalItems} songs matching "
                                {searchQuery}"
                            </>
                        ) : (
                            <>
                                Showing {songs.length} of{" "}
                                {pagination.totalItems} songs
                            </>
                        )}
                    </span>
                    <span>
                        Page {pagination.currentPage} of {pagination.totalPages}
                    </span>
                </StatsBar>

                <div data-aos="fade-up" data-aos-delay="700">
                    <SongList songs={songs} isLoading={isLoading} />
                </div>
                {pagination.totalPages > 1 && (
                    <div data-aos="fade-up" data-aos-delay="800">
                        <Pagination
                            currentPage={pagination.currentPage}
                            totalPages={pagination.totalPages}
                            onPageChange={handlePageChange}
                        />
                    </div>
                )}
            </ContentArea>
            <SongForm />
            <DeleteConfirmModal />
        </Container>
    );
};

export default MusicManagement;
