import { useState, useEffect } from 'react';

interface RateLimitStatus {
    hourlyLimit: number;
    minuteLimit: number;
    remaining?: number;
    reset?: string;
}

export const useRateLimitStatus = () => {
    const [rateLimitStatus, setRateLimitStatus] = useState<RateLimitStatus | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    const fetchRateLimitStatus = async () => {
        try {
            setIsLoading(true);
            const response = await fetch('/api/health');
            const data = await response.json();
            
            if (data.rateLimits) {
                setRateLimitStatus({
                    hourlyLimit: data.rateLimits.hourlyLimit,
                    minuteLimit: data.rateLimits.minuteLimit,
                    remaining: response.headers.get('X-RateLimit-Remaining') ? 
                        parseInt(response.headers.get('X-RateLimit-Remaining')!) : undefined,
                    reset: response.headers.get('X-RateLimit-Reset') || undefined,
                });
            }
        } catch (error) {
            console.warn('Failed to fetch rate limit status:', error);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchRateLimitStatus();
        
        // Refresh rate limit status every 30 seconds
        const interval = setInterval(fetchRateLimitStatus, 30000);
        
        return () => clearInterval(interval);
    }, []);

    return { rateLimitStatus, isLoading, refresh: fetchRateLimitStatus };
};
