import styled from "@emotion/styled";

const PaginationContainer = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
`;

const PaginationList = styled.ul`
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
`;

const PaginationItem = styled.li`
    display: flex;
`;

const PageInfo = styled.span`
    margin: 0 1rem;
    color: #6c757d;
    font-size: 0.9rem;
    white-space: nowrap;
`;

const PaginationButton = styled.button<{ active?: boolean }>`
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border: 2px solid #131314;
    border-bottom: 5px solid #131314;
    background: ${props => (props.active ? "#667eea" : "white")};
    color: ${props => (props.active ? "white" : "#495057")};
    border-radius: 6px;
    cursor: ${props => (props.disabled ? "not-allowed" : "pointer")};
    transition: all 0.2s ease;
    font-weight: ${props => (props.active ? "600" : "400")};
    opacity: ${props => (props.disabled ? "0.5" : "1")};

    &:hover:not(:disabled) {
        background: ${props => (props.active ? "#7bbf56" : "#e9ecef")};
        border-color: ${props => (props.active ? "#7bbf56" : "#adb5bd")};
    }

    &:disabled {
        pointer-events: none;
    }
`;
export {
    PaginationContainer,
    PaginationList,
    PaginationItem,
    PageInfo,
    PaginationButton,
};
