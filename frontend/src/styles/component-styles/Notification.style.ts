import styled from "@emotion/styled";

const NotificationsContainer = styled.div`
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    z-index: 1050;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 400px;
    pointer-events: none;

    > * {
        pointer-events: auto;
    }
`;

const Notification = styled.div<{
    type: "success" | "error" | "warning" | "info";
}>`
    background: ${props => {
        switch (props.type) {
            case "success":
                return props.theme.colors.success + "20"; // 20% opacity
            case "error":
                return props.theme.colors.error + "20";
            case "warning":
                return props.theme.colors.warning + "20";
            default:
                return props.theme.colors.primary + "20";
        }
    }};
    border: 2px solid
        ${props => {
            switch (props.type) {
                case "success":
                    return props.theme.colors.success;
                case "error":
                    return props.theme.colors.error;
                case "warning":
                    return props.theme.colors.warning;
                default:
                    return props.theme.colors.primary;
            }
        }};
    color: ${props => {
        switch (props.type) {
            case "success":
                return props.theme.colors.success;
            case "error":
                return props.theme.colors.error;
            case "warning":
                return props.theme.colors.warning;
            default:
                return props.theme.colors.primary;
        }
    }};
    padding: 0.75rem 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideIn 0.3s ease-out;
    border-bottom: 4px solid
        ${props => {
            switch (props.type) {
                case "success":
                    return props.theme.colors.success;
                case "error":
                    return props.theme.colors.error;
                case "warning":
                    return props.theme.colors.warning;
                default:
                    return props.theme.colors.primary;
            }
        }};
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;

const NotificationContent = styled.div`
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
`;

const NotificationIcon = styled.span`
    font-size: 1.25rem;
`;

const NotificationMessage = styled.span`
    font-weight: 500;
    line-height: 1.4;
    font-size: 0.9rem;
`;

const CloseButton = styled.button`
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    margin-left: 1rem;
    font-size: 1.2rem;
    font-weight: bold;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background: rgba(0, 0, 0, 0.1);
        transform: scale(1.1);
    }

    &:active {
        transform: scale(0.95);
    }
`;

export {
    NotificationsContainer,
    Notification,
    NotificationContent,
    NotificationIcon,
    NotificationMessage,
    CloseButton,
};
