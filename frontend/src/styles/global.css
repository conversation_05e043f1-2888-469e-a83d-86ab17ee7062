* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    font-family:
        -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
        "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.6;
    transition:
        background-color 0.3s ease,
        color 0.3s ease;
}

code {
    font-family:
        source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace;
}

/* Scrollbar styles */
::-webkit-scrollbar {
    width: 8px;
}

/* AOS Animation styles */
[data-aos] {
    pointer-events: none;
}

[data-aos].aos-animate {
    pointer-events: auto;
}

/* Focus styles */
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Theme-aware scrollbar */
::-webkit-scrollbar-track {
    background: var(--scrollbar-track, #f1f1f1);
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb, #888);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover, #555);
}
