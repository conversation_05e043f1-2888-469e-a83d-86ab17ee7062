import { useDispatch, useSelector } from "react-redux";
import { toggleTheme } from "@/store/slices/uiSlice";
import { RootState } from "@/store/store";
import styled from "styled-components";

const ThemeButton = styled.button`
    padding: 0.5rem;
    border: 1px solid ${props => props.theme.colors.border};
    border-radius: 50%;
    background: ${props => props.theme.colors.surface};
    color: ${props => props.theme.colors.text};
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;

    &:hover {
        background: ${props => props.theme.colors.primary};
        color: ${props => props.theme.colors.white};
        transform: scale(1.05);
    }

    &:active {
        transform: scale(0.95);
    }
`;

const ThemeSwitcher = () => {
    const dispatch = useDispatch();
    const theme = useSelector((state: RootState) => state.ui.theme);
    const isDarkMode = theme === "dark";

    const handleToggle = () => {
        dispatch(toggleTheme());
    };

    return (
        <ThemeButton
            onClick={handleToggle}
            title={`Switch to ${isDarkMode ? "light" : "dark"} mode`}
        >
            {isDarkMode ? "☀️" : "🌙"}
        </ThemeButton>
    );
};

export default ThemeSwitcher;
