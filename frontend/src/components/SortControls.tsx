import { useDispatch, useSelector } from "react-redux";
import { setSortBy, setSortOrder } from "@/store/slices/songsSlice";
import { RootState } from "@/store/store";
import { ISong } from "@/types/song";
import {
    SortContainer,
    SortLabel,
    SortSelect,
    SortButton,
} from "@/styles/component-styles/SortControls.style";

const SortControls = () => {
    const dispatch = useDispatch();
    const { sortBy, sortOrder } = useSelector(
        (state: RootState) => state.songs
    );

    const sortOptions: { value: keyof ISong; label: string }[] = [
        { value: "title", label: "Title" },
        { value: "artist", label: "Artist" },
        { value: "album", label: "Album" },
        { value: "year", label: "Year" },
        { value: "genre", label: "Genre" },
        { value: "duration", label: "Duration" },
    ];

    const handleSortByChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        dispatch(setSortBy(e.target.value as keyof ISong));
    };

    const handleSortOrderChange = (order: "asc" | "desc") => {
        dispatch(setSortOrder(order));
    };

    return (
        <SortContainer>
            {" "}
            {/* Replace {} with the appropriate theme object */}
            <SortLabel>Sort by:</SortLabel>
            <SortSelect value={sortBy} onChange={handleSortByChange}>
                {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </SortSelect>
            <SortButton
                active={sortOrder === "asc"}
                onClick={() => handleSortOrderChange("asc")}
            >
                ↑ Asc
            </SortButton>
            <SortButton
                active={sortOrder === "desc"}
                onClick={() => handleSortOrderChange("desc")}
            >
                ↓ Desc
            </SortButton>
        </SortContainer>
    );
};

export default SortControls;
