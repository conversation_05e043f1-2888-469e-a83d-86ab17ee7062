import { useDispatch, useSelector } from "react-redux";
import { setSortBy, setSortOrder } from "@/store/slices/songsSlice";
import { RootState } from "@/store/store";
import { ISong } from "@/types/song";
import styled from "@emotion/styled";

const SortContainer = styled.div<{ theme: any }>`
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem;
    background: ${props => props.theme.colors.surface};
    border-radius: 8px;
    border: 1px solid ${props => props.theme.colors.border};
`;

const SortLabel = styled.label<{ theme: any }>`
    font-size: 0.9rem;
    font-weight: 500;
    color: ${props => props.theme.colors.textSecondary};
`;

const SortSelect = styled.select<{ theme: any }>`
    padding: 0.5rem;
    border: 1px solid ${props => props.theme.colors.border};
    border-radius: 4px;
    background: ${props => props.theme.colors.background};
    color: ${props => props.theme.colors.text};
    font-size: 0.9rem;
    cursor: pointer;

    &:focus {
        outline: none;
        border-color: ${props => props.theme.colors.primary};
    }
`;

const SortButton = styled.button<{ active: boolean; theme: any }>`
    padding: 0.5rem 0.75rem;
    border: 1px solid
        ${props =>
            props.active
                ? props.theme.colors.primary
                : props.theme.colors.border};
    border-radius: 4px;
    background: ${props =>
        props.active
            ? props.theme.colors.primary
            : props.theme.colors.background};
    color: ${props =>
        props.active ? props.theme.colors.white : props.theme.colors.text};
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;

    &:hover {
        background: ${props =>
            props.active
                ? props.theme.colors.primaryDark
                : props.theme.colors.surface};
    }
`;

const SortControls = () => {
    const dispatch = useDispatch();
    const { sortBy, sortOrder } = useSelector(
        (state: RootState) => state.songs
    );

    const sortOptions: { value: keyof ISong; label: string }[] = [
        { value: "title", label: "Title" },
        { value: "artist", label: "Artist" },
        { value: "album", label: "Album" },
        { value: "year", label: "Year" },
        { value: "genre", label: "Genre" },
        { value: "duration", label: "Duration" },
    ];

    const handleSortByChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        dispatch(setSortBy(e.target.value as keyof ISong));
    };

    const handleSortOrderChange = (order: "asc" | "desc") => {
        dispatch(setSortOrder(order));
    };

    return (
        <SortContainer>
            <SortLabel>Sort by:</SortLabel>
            <SortSelect value={sortBy} onChange={handleSortByChange}>
                {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </SortSelect>

            <SortButton
                active={sortOrder === "asc"}
                onClick={() => handleSortOrderChange("asc")}
            >
                ↑ Asc
            </SortButton>
            <SortButton
                active={sortOrder === "desc"}
                onClick={() => handleSortOrderChange("desc")}
            >
                ↓ Desc
            </SortButton>
        </SortContainer>
    );
};

export default SortControls;
