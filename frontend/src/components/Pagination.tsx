import {
    PaginationContainer,
    <PERSON><PERSON>ation<PERSON>ist,
    PaginationItem,
    PaginationButton,
    PageInfo,
} from "@/styles/component-styles/Pagination.style";
import styled from "@emotion/styled";

const Pagination = ({
    currentPage,
    totalPages,
    onPageChange,
}: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
}) => {
    const getVisiblePages = () => {
        const delta = 2;
        const range = [];
        const rangeWithDots = [];

        for (
            let i = Math.max(2, currentPage - delta);
            i <= Math.min(totalPages - 1, currentPage + delta);
            i++
        ) {
            range.push(i);
        }

        if (currentPage - delta > 2) {
            rangeWithDots.push(1, "...");
        } else {
            rangeWithDots.push(1);
        }

        rangeWithDots.push(...range);

        if (currentPage + delta < totalPages - 1) {
            rangeWithDots.push("...", totalPages);
        } else {
            rangeWithDots.push(totalPages);
        }

        return rangeWithDots;
    };
    const visiblePages = totalPages > 1 ? getVisiblePages() : [1];

    return (
        <PaginationContainer>
            <PaginationList>
                <PaginationItem>
                    <PaginationButton
                        disabled={currentPage === 1}
                        onClick={() => onPageChange(currentPage - 1)}
                        title="Previous page"
                    >
                        ←
                    </PaginationButton>
                </PaginationItem>

                {visiblePages.map((page, index) => (
                    <PaginationItem key={index}>
                        {page === "..." ? (
                            <PaginationButton disabled>...</PaginationButton>
                        ) : (
                            <PaginationButton
                                active={page === currentPage}
                                onClick={() =>
                                    typeof page === "number" &&
                                    onPageChange(page)
                                }
                            >
                                {page}
                            </PaginationButton>
                        )}
                    </PaginationItem>
                ))}

                <PaginationItem>
                    <PaginationButton
                        disabled={currentPage === totalPages}
                        onClick={() => onPageChange(currentPage + 1)}
                        title="Next page"
                    >
                        →
                    </PaginationButton>
                </PaginationItem>
            </PaginationList>

            <PageInfo>
                Page {currentPage} of {totalPages}
            </PageInfo>
        </PaginationContainer>
    );
};

export default Pagination;
