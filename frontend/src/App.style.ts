import styled from "@emotion/styled";

export const AppContainer = styled.div`
    min-height: 100vh;
    max-width: 1200px;
    margin-inline: auto;
    padding: 20px;
`;

export const Header = styled.header`
    text-align: left;
    margin-bottom: 2rem;
    color: white;
`;

export const Title = styled.h1`
    font-size: 2.5rem;
    font-weight: 900;
    color: #6c757d;
    margin: 0;
`;

export const SubContainer = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
`;
export const Subtitle = styled.h1`
    font-size: 1.1rem;
    font-weight: 500;
    color: #6c757d;
`;

export const Fun = styled.p`
    font-weight: 800;
`;
