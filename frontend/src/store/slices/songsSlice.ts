import { ISong } from "@app-types/song";
import { createSlice } from "@reduxjs/toolkit";

// Helper function to handle filter and pagination
const filterAndPaginateSongs = (
    allSongs: ISong[],
    searchQuery: string,
    currentPage: number,
    itemsPerPage: number,
    sortBy: keyof ISong,
    sortOrder: "asc" | "desc"
) => {
    // Filter songs based on search query
    let filteredSongs = allSongs;
    if (searchQuery && searchQuery.trim()) {
        const searchLower = searchQuery.toLowerCase().trim();
        filteredSongs = allSongs.filter(
            song =>
                song.title.toLowerCase().includes(searchLower) ||
                song.artist.toLowerCase().includes(searchLower) ||
                song.album.toLowerCase().includes(searchLower) ||
                song.genre.toLowerCase().includes(searchLower) ||
                song.year.toString().includes(searchLower)
        );
    }

    // Sort songs
    filteredSongs.sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        // Handle different data types
        if (typeof aValue === "string") {
            aValue = aValue.toLowerCase();
            bValue = typeof bValue === "string" ? bValue.toLowerCase() : bValue;
        }

        if (sortOrder === "asc") {
            return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
            return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
    });

    // Calculate pagination
    const totalItems = filteredSongs.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedSongs = filteredSongs.slice(startIndex, endIndex);

    return {
        songs: paginatedSongs,
        pagination: {
            currentPage,
            totalPages,
            totalItems,
            itemsPerPage,
        },
    };
};

const initialState = {
    allSongs: [] as ISong[],
    songs: [] as ISong[],
    currentSong: null,
    isLoading: false,
    error: null,
    searchQuery: "",
    sortBy: "title" as keyof ISong, // Default sort by title
    sortOrder: "asc" as "asc" | "desc", // Default sort order ascending
    pagination: {
        currentPage: 1,
        totalPages: 1,
        itemsPerPage: 9, // Show 9 items per page
        totalItems: 0,
    },
};

const songsSlice = createSlice({
    name: "songs",
    initialState,
    reducers: {
        // Fetch songs action
        fetchSongsRequest: state => {
            state.isLoading = true;
            state.error = null;
        },
        fetchSongsSuccess: (state, action) => {
            state.isLoading = false;
            // For server-side pagination, we just set the songs directly
            state.songs = action.payload;
            state.allSongs = action.payload; // Keep for compatibility
            state.error = null;

            // Update pagination info (this will be updated when we get proper pagination response from backend)
            state.pagination.totalItems = action.payload.length;
            state.pagination.totalPages = Math.ceil(
                action.payload.length / state.pagination.itemsPerPage
            );
        },
        fetchSongsFailure: (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        },

        // Create song action
        createSongRequest: (state, action) => {
            state.isLoading = true;
            state.error = null;
        },
        createSongSuccess: (state, action) => {
            state.isLoading = false;
            state.allSongs.unshift(action.payload);

            // Reapply current filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
            state.error = null;
        },
        createSongFailure: (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        },
        // Update song action
        updateSongRequest: (state, action) => {
            state.isLoading = true;
            state.error = null;
        },
        updateSongSuccess: (state, action) => {
            state.isLoading = false;
            const index = state.allSongs.findIndex(
                song => song.id === action.payload.id
            );
            if (index !== -1) {
                state.allSongs[index] = action.payload;
            }

            // Reapply filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
            state.error = null;
        },
        updateSongFailure: (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        },

        // Delete song action
        deleteSongRequest: (state, action) => {
            state.isLoading = true;
            state.error = null;
        },
        deleteSongSuccess: (state, action) => {
            state.isLoading = false;
            state.allSongs = state.allSongs.filter(
                song => song.id !== action.payload.id
            );

            // Reapply filters and pagination
            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
            state.error = null;
        },
        deleteSongFailure: (state, action) => {
            state.isLoading = false;
            state.error = action.payload;
        },

        // UI state actions
        setCurrentSong: (state, action) => {
            state.currentSong = action.payload;
        },
        clearCurrentSong: state => {
            state.currentSong = null;
        },
        setSearchQuery: (state, action) => {
            state.searchQuery = action.payload;
            state.pagination.currentPage = 1; // Reset to first page when searching
        },

        // Sort actions (sortBy and sortOrder)
        setSortBy: (state, action) => {
            state.sortBy = action.payload;
        },

        setSortOrder: (state, action) => {
            state.sortOrder = action.payload;
        },

        // Set current page action
        setCurrentPage: (state, action) => {
            state.pagination.currentPage = action.payload;

            const result = filterAndPaginateSongs(
                state.allSongs,
                state.searchQuery,
                state.pagination.currentPage,
                state.pagination.itemsPerPage,
                state.sortBy,
                state.sortOrder
            );

            state.songs = result.songs;
            state.pagination = result.pagination;
        },
        clearError: state => {
            state.error = null;
        },
    },
});

export const {
    fetchSongsRequest,
    fetchSongsSuccess,
    fetchSongsFailure,
    createSongRequest,
    createSongSuccess,
    createSongFailure,
    updateSongRequest,
    updateSongSuccess,
    updateSongFailure,
    deleteSongRequest,
    deleteSongSuccess,
    deleteSongFailure,
    setCurrentSong,
    clearCurrentSong,
    setSearchQuery,
    setSortBy,
    setSortOrder,
    setCurrentPage,
    clearError,
} = songsSlice.actions;
export default songsSlice.reducer;
