{"name": "project-addis-frontend", "version": "1.0.0", "description": "React frontend for project-addis", "main": "src/index.tsx", "scripts": {"dev": "webpack serve --config webpack.config.ts --mode development", "build": "webpack --config webpack.config.ts --mode production", "format": "prettier --write .", "start": "webpack serve --mode development", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["react", "redux", "emotion", "webpack", "songs"], "author": "", "license": "MIT", "dependencies": {"@emotion/css": "^11.13.5", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@reduxjs/toolkit": "^2.8.2", "aos": "^2.3.4", "axios": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "redux-saga": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@types/aos": "^3.0.7", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "^8.37.0", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "prettier": "^3.6.2", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "url-loader": "^4.1.1", "webpack": "^5.100.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}}